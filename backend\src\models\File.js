// backend/src/models/File.js
// Example File model to track uploaded files, their Replicate jobs, and transcription status
// This is a placeholder. Implement if you add data persistence for files.
/*
const mongoose = require('mongoose');

const FileSchema = new mongoose.Schema({
  userId: { // Link to User model if you have user accounts
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    // required: true, // Make required if files must belong to a user
  },
  originalFilename: {
    type: String,
    required: true,
  },
  serverFilename: { // The unique filename generated by multer (e.g., mediaFile-timestamp-random.mp4)
    type: String,
    required: true,
    unique: true,
  },
  mimetype: {
    type: String,
  },
  size: { // File size in bytes
    type: Number,
  },
  storageLocation: { // 'local', 's3', 'gcs', etc.
    type: String,
    default: 'local', 
  },
  storagePathOrUrl: { // Full path on local disk, or URL if in cloud storage
    type: String,
    required: true,
  },
  replicateJobId: {
    type: String,
    index: true, // Index for faster lookups if you query by job ID
  },
  replicateJobStatus: {
    type: String, // 'starting', 'processing', 'succeeded', 'failed', 'canceled'
  },
  transcriptionOutputUrlVTT: { // URL to the VTT file (e.g., from Replicate or your own storage)
    type: String,
  },
  transcriptionOutputUrlSRT: {
    type: String,
  },
  transcriptionSegments: { // Store the segments array directly if preferred
    type: mongoose.Schema.Types.Mixed,
  },
  detectedLanguage: {
    type: String,
  },
  uploadedAt: {
    type: Date,
    default: Date.now,
  },
  transcriptionCompletedAt: {
    type: Date,
  },
  lastCheckedAt: { // For polling Replicate status
    type: Date,
  }
});

module.exports = mongoose.model('File', FileSchema);
*/
