/* frontend/src/styles/global.css */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8f9fa; /* Lighter gray */
  color: #212529; /* Darker text */
  line-height: 1.6;
}

.app-container { /* Renamed from .App to avoid conflict if App.js has its own styles */
  max-width: 960px; /* Slightly narrower for better readability */
  margin: 20px auto; /* Add some top/bottom margin */
  padding: 20px;
  background-color: #ffffff; /* White background for content area */
  border-radius: 8px; /* Rounded corners */
  box-shadow: 0 2px 10px rgba(0,0,0,0.1); /* Subtle shadow */
}

nav {
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #dee2e6; /* Lighter border */
}

nav a {
  margin-right: 15px;
  text-decoration: none;
  color: #007bff; /* Bootstrap primary blue */
  font-weight: 500;
}
nav a:hover {
  text-decoration: underline;
  color: #0056b3; /* Darker blue on hover */
}


button {
  padding: 10px 15px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1em;
  transition: background-color 0.2s ease-in-out;
}

button:hover:not(:disabled) {
  background-color: #0056b3;
}

button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  opacity: 0.7;
}

input[type="file"] {
  margin-bottom: 10px;
  padding: 8px;
  border: 1px solid #ced4da; /* Standard input border */
  border-radius: 4px;
}

.subtitle-display {
  margin-top: 15px;
  padding: 12px;
  background-color: #e9ecef; /* Light gray background */
  border-radius: 4px;
  min-height: 2.5em; 
  text-align: center;
  font-size: 1.1em; /* Slightly larger subtitle text */
  border: 1px solid #ced4da;
}
.subtitle-display p {
  margin: 0;
}

.esl-controls {
  margin-top: 20px;
  padding: 15px;
  background-color: #f1f3f5; /* Even lighter gray for control panel */
  border-radius: 4px;
  border: 1px solid #dee2e6;
}
.esl-controls h4 {
  margin-top: 0;
  margin-bottom: 10px;
}
.esl-controls button {
  margin-right: 8px;
  margin-bottom: 8px; /* For wrapping */
  background-color: #28a745; /* Green for ESL controls */
}
.esl-controls button:hover:not(:disabled) {
  background-color: #1e7e34; /* Darker green */
}

h1, h2, h3 {
  color: #343a40; /* Dark gray for headers */
}
