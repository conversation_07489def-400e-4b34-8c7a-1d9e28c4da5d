{"name": "whisperx-backend", "version": "1.0.0", "description": "Backend for WhisperX ESL App", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint . --fix"}, "dependencies": {"@ffmpeg-installer/ffmpeg": "^1.1.0", "@ffprobe-installer/ffprobe": "^2.1.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "fluent-ffmpeg": "^2.1.2", "mongoose": "^7.5.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "replicate": "^0.20.1"}, "devDependencies": {"eslint": "^8.48.0", "nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}, "author": "Your Name/Team", "license": "ISC"}