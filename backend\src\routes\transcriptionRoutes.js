// backend/src/routes/transcriptionRoutes.js
const express = require('express');
const router = express.Router();
const transcriptionController = require('../controllers/transcriptionController');

// POST /api/transcribe/start - Start a new transcription job with Replicate
// Body should contain { fileUrl: "publicly_accessible_url_to_media" }
// Optionally: { language: "en", model: "large-v2" }
router.post('/start', transcriptionController.startTranscriptionJob);

// GET /api/transcribe/status/:jobId - Get the status and output of a Replicate transcription job
router.get('/status/:jobId', transcriptionController.getTranscriptionStatus);

// (Future) GET /api/transcribe/vtt/:fileIdOrJobId - Serve a VTT file (if stored/generated by backend)
// router.get('/vtt/:identifier', transcriptionController.getVTTFile);


module.exports = router;
