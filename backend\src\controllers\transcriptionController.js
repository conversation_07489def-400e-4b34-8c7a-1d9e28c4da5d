// backend/src/controllers/transcriptionController.js
const replicateService = require('../services/replicateService');
// const subtitleGeneratorService = require('../services/subtitleGeneratorService'); // If generating VTT/SRT manually

// Simple in-memory storage for job metadata (replace with database in production)
const jobMetadata = new Map();

exports.startTranscriptionJob = async (req, res) => {
  const { fileUrl, fileId, language, model } = req.body;

  // Support both fileUrl (for external URLs) and fileId (for local files)
  if (!fileUrl && !fileId) {
    return res.status(400).json({ message: 'Either fileUrl or fileId is required to start transcription.' });
  }

  try {
    let job;

    if (fileId) {
      // Check file size and decide approach
      const path = require('path');
      const fs = require('fs');
      const uploadsDir = path.join(__dirname, '../../uploads');
      const localFilePath = path.join(uploadsDir, fileId);

      // Check if file exists
      if (!fs.existsSync(localFilePath)) {
        return res.status(404).json({ message: 'Uploaded file not found.' });
      }

      // Get file size
      const stats = fs.statSync(localFilePath);
      const fileSizeInMB = stats.size / (1024 * 1024);
      const MAX_BASE64_SIZE_MB = 50; // Limit base64 to 50MB files

      console.log(`File size: ${fileSizeInMB.toFixed(2)}MB`);

      if (fileSizeInMB <= MAX_BASE64_SIZE_MB) {
        // Use base64 approach for smaller files
        console.log(`Using base64 approach for file: ${localFilePath}`);
        job = await replicateService.startTranscriptionFromFile(localFilePath, language, model);
      } else {
        // Use URL approach for larger files
        console.log(`File too large for base64 (${fileSizeInMB.toFixed(2)}MB), using URL approach`);
        const ngrokUrl = process.env.SERVER_PUBLIC_URL;
        if (!ngrokUrl) {
          return res.status(500).json({ message: 'Server public URL not configured for large files.' });
        }
        const fileUrl = `${ngrokUrl}/uploads/${fileId}`;
        console.log(`Using URL approach: ${fileUrl}`);
        job = await replicateService.startTranscription(fileUrl, language, model);
      }
    } else {
      // Use URL approach (for external URLs)
      console.log(`Starting transcription using URL: ${fileUrl}`);
      job = await replicateService.startTranscription(fileUrl, language, model);
    }
    if (!job || !job.id) {
        throw new Error("Failed to create transcription job with Replicate.");
    }

    // Store job metadata for later retrieval
    jobMetadata.set(job.id, {
      originalFileUrl: fileUrl || `local:${fileId}`,
      fileId: fileId,
      language: language,
      model: model,
      createdAt: new Date(),
      status: job.status
    });

    res.status(202).json({
        message: 'Transcription job started with Replicate.',
        jobId: job.id,
        status: job.status
    });
  } catch (error) {
    console.error('Error starting transcription job with Replicate:', error);
    res.status(500).json({ message: 'Failed to start transcription job.', error: error.message });
  }
};

exports.getTranscriptionStatus = async (req, res) => {
  const { jobId } = req.params;
  if (!jobId) {
    return res.status(400).json({ message: 'Job ID is required.' });
  }

  try {
    const jobDetails = await replicateService.getTranscriptionStatus(jobId);

    // The Replicate output for Whisper models often includes:
    // jobDetails.output.segments: array of {start, end, text, words: [{start, end, word}]}
    // jobDetails.output.vtt: URL to a VTT file
    // jobDetails.output.srt: URL to an SRT file
    // jobDetails.output.text: Full plain text transcription
    // jobDetails.output.detected_language: Detected language
    // jobDetails.input.audio: The original audio URL passed to Replicate

    // If Replicate has removed the input data, restore it from our metadata
    const metadata = jobMetadata.get(jobId);
    if (metadata && (!jobDetails.input || Object.keys(jobDetails.input).length === 0)) {
      console.log(`Restoring input data for job ${jobId} from local metadata`);
      jobDetails.input = {
        audio_file: metadata.originalFileUrl,
        language: metadata.language,
        model: metadata.model
      };
    }

    res.status(200).json(jobDetails); // Send the full job details from Replicate

  } catch (error) {
    console.error('Error fetching transcription status from Replicate:', error);
    // Replicate API might return specific errors, pass them through if possible
    if (error.response && error.response.data) {
        return res.status(error.response.status || 500).json({
            message: 'Failed to fetch transcription status from Replicate.',
            replicateError: error.response.data
        });
    }
    res.status(500).json({ message: 'Failed to fetch transcription status.', error: error.message });
  }
};
