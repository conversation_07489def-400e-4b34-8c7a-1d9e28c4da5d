# Backend Environment Variables - Copy to .env and fill in your values

# Server Configuration
PORT=5000
NODE_ENV=development # 'production' or 'development'
# FRONTEND_URL="http://localhost:3000" # For CORS configuration if needed

# If running locally and need Replicate to access files from this server (via ngrok or similar):
# SERVER_PUBLIC_URL="https://your-ngrok-subdomain.ngrok.io" # Example: https://1234abcd.ngrok.free.app

# Replicate API Configuration
REPLICATE_API_TOKEN="your_replicate_api_token_here"
# Find the correct Whisper (or WhisperX-like) model version string on Replicate.
# It looks like "owner/model-name:versionhash"
# Example for a generic Whisper model (check Replicate for the best one for your needs, especially one with word timestamps):
# REPLICATE_WHISPER_MODEL_VERSION="openai/whisper:4d50797290df275329f8b422a0aaef5535eb09d590a141599e049950ddfd1002"
REPLICATE_WHISPER_MODEL_VERSION="REPLACE_WITH_ACTUAL_WHISPER_MODEL_ON_REPLICATE"

# (Optional) Replicate Webhook URL for notifications (must be publicly accessible)
# REPLICATE_WEBHOOK_URL="https://your-backend-domain.com/api/replicate-webhook"

# (Optional) HuggingFace Token if required by the specific Replicate model
# HF_TOKEN="your_huggingface_token_if_needed"

# (Optional) File Storage (Example for AWS S3) - Uncomment and fill if using S3
# AWS_ACCESS_KEY_ID=your_aws_access_key_id
# AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
# AWS_REGION=your_aws_region
# S3_BUCKET_NAME=your_s3_bucket_name

# (Optional) Database (Example for MongoDB) - Uncomment and fill if using MongoDB
# MONGO_URI=mongodb://localhost:27017/whisperx_app_db_dev # Or your cloud MongoDB URI

# (Optional) JWT Secret for authentication if you implement user accounts
# JWT_SECRET=your_very_strong_and_random_jwt_secret_key
