// backend/src/config/index.js
// This file can be used to export consolidated configuration objects
// For now, most config is directly accessed via process.env in services
// or server.js.
// Example:
// module.exports = {
//   port: process.env.PORT || 5000,
//   nodeEnv: process.env.NODE_ENV || 'development',
//   replicateApiToken: process.env.REPLICATE_API_TOKEN,
//   replicateModelVersion: process.env.REPLICATE_WHISPER_MODEL_VERSION,
//   // Add other configurations
// };
